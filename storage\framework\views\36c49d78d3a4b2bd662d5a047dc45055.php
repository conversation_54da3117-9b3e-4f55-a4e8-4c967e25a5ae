<?php $__env->startSection('content'); ?>
    <div class="row justify-content-center">
        <div class="col-md-12">
            <div class="card card-custom">
                <div class="card-body">
                    <!-- Progress Steps -->
                    <div class="progress-steps-container mb-5">
                        <div class="progress-line">
                            <div class="progress-line-inner" style="width: <?php echo e(($currentStep / $totalSteps) * 100); ?>%;">
                            </div>
                        </div>
                        <div class="progress-steps">
                            <div class="progress-step <?php echo e($currentStep >= 1 ? 'active' : ''); ?>">
                                <div class="step-circle">1</div>
                                <div class="step-label">Upload</div>
                            </div>
                            <div class="progress-step <?php echo e($currentStep >= 2 ? 'active' : ''); ?>">
                                <div class="step-circle">2</div>
                                <div class="step-label">Create</div>
                            </div>
                            <div class="progress-step <?php echo e($currentStep >= 3 ? 'active' : ''); ?>">
                                <div class="step-circle">3</div>
                                <div class="step-label">Done</div>
                            </div>
                        </div>
                    </div>

                    <!-- Import Information -->
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="alert">
                                <h6><i class="fas fa-user-md"></i> Assigned Provider</h6>
                                <p class="mb-2">
                                    <strong><?php echo e($selectedProvider->first_name); ?>

                                        <?php echo e($selectedProvider->last_name); ?></strong>
                                    <?php if($selectedProvider->clinic_name): ?>
                                        <br><small class="text-muted"><?php echo e($selectedProvider->clinic_name); ?></small>
                                    <?php endif; ?>
                                </p>
                                <p class="mb-0"><small class="text-muted">All scripts have been assigned to this
                                        provider and are available in their "Ready to Sign" section.</small></p>
                            </div>
                        </div>
                    </div>

                    <!-- Scripts List -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Imported Scripts</h5>
                        </div>
                        <div class="d-flex justify-content-end">
                            <button id="download-all-btn" class="btn btn-primary m-2"
                                style="background-color: #000000; border-color: #000000;">
                                <i class="fas fa-download"></i> Download All
                            </button>
                            <button id="download-selected-btn" class="btn btn-primary m-2" disabled
                                style="background-color: #000000; border-color: #000000;">
                                <i class="fas fa-download"></i> Download Selected
                            </button>
                            <a href="<?php echo e($has_back); ?>" class="btn btn-primary m-2"
                                style="background-color: #000000; border-color: #000000;">
                                <i class="fas fa-list"></i> Back to Home
                            </a>
                        </div>
                        <div class="card-body">
                            <div class="datatable datatable-bordered datatable-head-custom" id="prescription_dt"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('styles'); ?>
    <style>
        .alert {
            background-color: #dff9ff;
            color: #333;
            border: none;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 15px;
        }

        /* Progress Steps Styling */
        .progress-steps-container {
            position: relative;
            padding: 20px 0;
            margin: 0 auto;
            max-width: 700px;
        }

        .progress-steps {
            display: flex;
            justify-content: space-between;
            position: relative;
            z-index: 1;
        }

        .progress-step {
            text-align: center;
            width: 20%;
            position: relative;
        }

        .step-circle {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background-color: #e9ecef;
            color: #6c757d;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 8px;
            font-weight: 500;
            font-size: 14px;
        }

        .progress-step.active .step-circle {
            background-color: #000000;
            color: white;
        }

        .step-label {
            font-size: 13px;
            color: #6c757d;
            font-weight: 400;
        }

        .progress-step.active .step-label {
            color: #000000;
            font-weight: 500;
        }

        .progress-line {
            position: absolute;
            top: 38px;
            left: 10%;
            right: 10%;
            height: 2px;
            background-color: #e9ecef;
            z-index: 0;
        }

        .progress-line-inner {
            height: 100%;
            background-color: #000000;
        }
    </style>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('scripts'); ?>
    <script src="https://cdn.jsdelivr.net/gh/alpinejs/alpine@v2.7.0/dist/alpine.min.js" defer></script>
    <script>
        $(document).ready(function() {
            var datatable;
            var datatableElement;
            var columnArray;

            const storagePath = `<?php echo e(url('/storage')); ?>`;
            const apiRoute = `<?php echo e(route('prescription-list.api', ['importId' => $import_id])); ?>`;
            let url = "<?php echo e(Storage::url('/')); ?>";

            datatableElement = $('#prescription_dt');


            columnArray = [{
                    field: 'Select',
                    title: '<label class="checkbox">Select all &nbsp; <input type="checkbox" id="select-all-checkbox" /><span></span></label>',
                    sortable: false,
                    width: 100,
                    autoHide: false,
                    template: function(data) {
                        return `<label class="checkbox">
                        <input type="checkbox" class="prescription-checkbox" data-id="${data.id}" />
                        <span></span>
                    </label>`;
                    }
                },
                {
                    field: 'number',
                    title: `#`,
                    width: 50,
                    autoHide: false,
                }, {
                    field: 'script_date',
                    title: 'Script Date',
                    width: 'auto',
                    sortable: true,
                    autoHide: false,
                    template: function(data) {
                        return moment(data.script_date).format('MM/DD/YYYY');
                    }
                },
                {
                    field: 'first_name',
                    title: 'First Name',
                    width: 'auto',
                    sortable: true,
                    autoHide: false,
                },
                {
                    field: 'last_name',
                    title: 'Last Name',
                    width: 'auto',
                    sortable: true,
                    autoHide: false,
                },
                {
                    field: 'medication',
                    title: 'Medication',
                    width: 'auto',
                    sortable: true,
                    autoHide: false,
                },
                {
                    field: 'status',
                    title: 'Status',
                    width: 'auto',
                    sortable: true,
                    autoHide: false,
                    template: function(data) {
                        return data.status;
                    }
                }
            ];

            datatable = datatableElement.KTDatatable({
                data: {
                    type: 'remote',
                    source: {
                        read: {
                            url: apiRoute,
                            method: 'POST',
                            headers: {
                                'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
                            },
                            params: function() {
                                return {
                                    import_id: '<?php echo e($import_id); ?>',
                                };
                            },
                            map: function(raw) {
                                console.log('API Response:', raw);
                                var dataSet = raw;
                                if (typeof raw.data !== 'undefined') {
                                    dataSet = raw.data;
                                }
                                return dataSet;
                            },
                        },
                    },
                    pageSize: 10,
                    serverPaging: true,
                    serverFiltering: true,
                    serverSorting: true,
                },
                pagination: true,
                sortable: true,
                columns: columnArray,
            });
        });

        // Function to save selected IDs
        function saveSelectedIds() {
            // Get all currently visible checkboxes
            const visibleCheckboxes = $('.prescription-checkbox');

            // For each visible checkbox, update the selection state in our array
            visibleCheckboxes.each(function() {
                const id = parseInt($(this).data('id'), 10);
                const isChecked = $(this).prop('checked');

                if (id) {
                    // If checked, add to selectedIds if not already there
                    if (isChecked && !selectedIds.includes(id)) {
                        selectedIds.push(id);
                    }
                    // If unchecked, remove from selectedIds if it's there
                    else if (!isChecked && selectedIds.includes(id)) {
                        selectedIds = selectedIds.filter(selectedId => selectedId !== id);
                    }
                }
            });

            console.log('Updated selected IDs:', selectedIds);
        }

        // Function to restore selected IDs
        function restoreSelectedIds() {
            console.log('Restoring selections for IDs:', selectedIds);

            // First uncheck all checkboxes
            $('.prescription-checkbox').prop('checked', false);

            // Then check only those that were previously selected
            let restoredCount = 0;
            $('.prescription-checkbox').each(function() {
                const id = parseInt($(this).data('id'), 10);
                if (id && selectedIds.includes(id)) {
                    $(this).prop('checked', true);
                    restoredCount++;
                }
            });

            // Update the "Select All" checkbox state
            const totalCheckboxes = $('.prescription-checkbox').length;
            const checkedCheckboxes = $('.prescription-checkbox:checked').length;

            if (totalCheckboxes === checkedCheckboxes && totalCheckboxes > 0) {
                $('#select-all-checkbox').prop('checked', true);
            } else {
                $('#select-all-checkbox').prop('checked', false);
            }

            // Update button states
            updateButtonStates();
            console.log('Restored ' + restoredCount + ' selections out of ' + selectedIds.length + ' saved IDs');
        }

        // Handle datatable layout updates
        datatable.on('datatable-on-layout-updated', function() {
            console.log('Datatable layout updated');

            // After layout update, restore selections
            setTimeout(restoreSelectedIds, 300);

            // Handle "Select All" checkbox
            $('#select-all-checkbox').off('change').on('change', function() {
                const isChecked = $(this).prop('checked');
                $('.prescription-checkbox').prop('checked', isChecked);

                // Save the selection state
                saveSelectedIds();

                // Update button states
                updateButtonStates();

                // If we have checkboxes and they're all checked, disable "All" buttons
                if (isChecked && $('.prescription-checkbox').length > 0) {
                    $('#download-all-btn').prop('disabled', true);
                    $('#download-selected-btn').prop('disabled', false);
                } else if (!isChecked) {
                    // If unchecked, enable "All" buttons and disable "Selected" buttons
                    $('#download-all-btn').prop('disabled', false);
                    $('#download-selected-btn').prop('disabled', true);
                }
            });

            // Handle individual checkbox selection
            $('.prescription-checkbox').off('change').on('change', function() {
                // Save the selection state
                saveSelectedIds();

                // Update button states
                updateButtonStates();

                // Update "Select All" checkbox state
                const totalCheckboxes = $('.prescription-checkbox').length;
                const checkedCheckboxes = $('.prescription-checkbox:checked').length;

                if (totalCheckboxes === checkedCheckboxes && totalCheckboxes > 0) {
                    $('#select-all-checkbox').prop('checked', true);
                } else {
                    $('#select-all-checkbox').prop('checked', false);
                }
            });

            // Add row click handler for checkbox selection
            $('#prescription_dt tbody tr td').off('click').on('click', function(e) {
                // Skip if clicking on the checkbox cell
                if ($(this).is(':first-child') ||
                    $(e.target).is('input[type="checkbox"]') ||
                    $(e.target).closest('a').length ||
                    $(e.target).closest('button').length ||
                    $(e.target).closest('i').length) {
                    return;
                }

                // Find the checkbox in the first cell
                const checkbox = $(this).closest('tr').find('td:first-child input[type="checkbox"]');

                // Toggle the checkbox
                checkbox.prop('checked', !checkbox.prop('checked'));

                // Trigger change event
                checkbox.trigger('change');
            });

            // Restore selections after layout is updated
            restoreSelectedIds();
        });

        // Handle download all button
        $('#download-all-btn').on('click', function() {
            // Get the current status from the URL or page variable
            const currentStatus = '<?php echo e($statusText ?? 'created'); ?>';
            const importId = '<?php echo e($import_id); ?>';

            // Simplify our approach - just get IDs from the DOM
            const visibleIds = [];

            // Try to get IDs from the DOM
            $('.prescription-checkbox').each(function() {
                const id = $(this).data('id');
                if (id) {
                    visibleIds.push(parseInt(id, 10));
                }
            });

            // Create a form to submit
            const form = $('<form>', {
                'method': 'POST',
                'action': '<?php echo e(route('excel.download-all-pdf', ['importId' => $import_id])); ?>'
            });

            // Add CSRF token
            form.append($('<input>', {
                'type': 'hidden',
                'name': '_token',
                'value': '<?php echo e(csrf_token()); ?>'
            }));

            // Add status parameter
            form.append($('<input>', {
                'type': 'hidden',
                'name': 'status',
                'value': currentStatus
            }));

            // If we found IDs, include them
            if (visibleIds.length > 0) {
                visibleIds.forEach(id => {
                    form.append($('<input>', {
                        'type': 'hidden',
                        'name': 'displayed_ids[]',
                        'value': id
                    }));
                });
            } else {
                // If no IDs found, use the all_with_status flag
                form.append($('<input>', {
                    'type': 'hidden',
                    'name': 'all_with_status',
                    'value': 'true'
                }));
            }

            // Add to body and submit
            $('body').append(form);
            form.submit();
            form.remove();
        });

        // Handle download selected button
        $('#download-selected-btn').on('click', function() {
            const selectedIds = getSelectedIds();
            if (selectedIds.length === 0) return;

            // Create a form to submit the selected IDs
            const form = $('<form>', {
                'method': 'POST',
                'action': '<?php echo e(route('excel.download-selected-pdf')); ?>'
            });

            // Add CSRF token
            form.append($('<input>', {
                'type': 'hidden',
                'name': '_token',
                'value': '<?php echo e(csrf_token()); ?>'
            }));

            // Add selected IDs
            selectedIds.forEach(id => {
                form.append($('<input>', {
                    'type': 'hidden',
                    'name': 'ids[]',
                    'value': id
                }));
            });

            // Add to body and submit
            $('body').append(form);
            form.submit();
            form.remove();
        });
        
        // Helper function to get selected IDs
        function getSelectedIds() {
            // First save the current selection state to ensure it's up to date
            saveSelectedIds();
            console.log('Getting selected IDs:', selectedIds);
            return selectedIds;
        }
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\KodeCreators\newlife-panel\resources\views/excel-import/staff-view.blade.php ENDPATH**/ ?>