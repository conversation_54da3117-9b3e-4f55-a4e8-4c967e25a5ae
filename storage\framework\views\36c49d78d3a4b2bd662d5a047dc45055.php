<?php $__env->startSection('content'); ?>
    <div class="row justify-content-center">
        <div class="col-md-12">
            <div class="card card-custom">
                <div class="card-header">
                    <div class="card-title">
                        <h3 class="card-label">Staff Bulk Import Results</h3>
                    </div>
                </div>

                <div class="card-body">
                    <!-- Progress Steps -->
                    <div class="progress-steps-container mb-5">
                        <div class="progress-line">
                            <div class="progress-line-inner" style="width: <?php echo e(($currentStep / $totalSteps) * 100); ?>%;">
                            </div>
                        </div>
                        <div class="progress-steps">
                            <div class="progress-step <?php echo e($currentStep >= 1 ? 'active' : ''); ?>">
                                <div class="step-circle">1</div>
                                <div class="step-label">Upload</div>
                            </div>
                            <div class="progress-step <?php echo e($currentStep >= 2 ? 'active' : ''); ?>">
                                <div class="step-circle">2</div>
                                <div class="step-label">Create</div>
                            </div>
                            <div class="progress-step <?php echo e($currentStep >= 3 ? 'active' : ''); ?>">
                                <div class="step-circle">3</div>
                                <div class="step-label">Done</div>
                            </div>
                        </div>
                    </div>

                    <!-- Import Information -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="alert">
                                <h6><i class="fas fa-user-md"> </i> Assigned Provider</h6>
                                <p class="mb-2">
                                    <strong><?php echo e($selectedProvider->first_name); ?>

                                        <?php echo e($selectedProvider->last_name); ?></strong>
                                    <?php if($selectedProvider->clinic_name): ?>
                                        <br><small class="text-muted"><?php echo e($selectedProvider->clinic_name); ?></small>
                                    <?php endif; ?>
                                </p>
                                <p class="mb-0"><small class="text-muted">All scripts have been assigned to this
                                        provider and are available in their "Ready to Sign" section.</small></p>
                            </div>
                        </div>
                    </div>

                    <!-- Scripts List -->
                    <div class="card">
                        <div class="card-header">
                            <div class="card-title">
                                <h5 class="mb-0">Imported Scripts</h5>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="datatable datatable-bordered datatable-head-custom" id="prescription_dt"></div>
                        </div>
                    </div>

                    <!-- Navigation Buttons -->
                    <div class="d-flex justify-content-between mt-4">
                        <a href="<?php echo e($has_back); ?>" class="btn btn-outline-secondary px-4 py-2">
                            <i class="fas fa-arrow-left mr-1"></i> Back
                        </a>
                        <div>
                            <a href="<?php echo e(route('excel.staff-bulk-import')); ?>" class="btn btn-primary px-4 py-2">
                                <i class="fas fa-plus mr-1"></i> New Import
                            </a>
                            <a href="<?php echo e(route('scripts.all')); ?>" class="btn btn-info px-4 py-2 ml-2">
                                <i class="fas fa-list mr-1"></i> View All Scripts
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('styles'); ?>
    <style>
        /* Progress Steps Styling */
        .progress-steps-container {
            position: relative;
            padding: 20px 0;
            margin: 0 auto;
            max-width: 700px;
        }

        .progress-steps {
            display: flex;
            justify-content: space-between;
            position: relative;
            z-index: 1;
        }

        .progress-step {
            text-align: center;
            width: 20%;
            position: relative;
        }

        .step-circle {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background-color: #e9ecef;
            color: #6c757d;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 8px;
            font-weight: 500;
            font-size: 14px;
        }

        .progress-step.active .step-circle {
            background-color: #000000;
            color: white;
        }

        .step-label {
            font-size: 13px;
            color: #6c757d;
            font-weight: 400;
        }

        .progress-step.active .step-label {
            color: #000000;
            font-weight: 500;
        }

        .progress-line {
            position: absolute;
            top: 38px;
            left: 10%;
            right: 10%;
            height: 2px;
            background-color: #e9ecef;
            z-index: 0;
        }

        .progress-line-inner {
            height: 100%;
            background-color: #000000;
        }
    </style>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('scripts'); ?>
    <script src="https://cdn.jsdelivr.net/gh/alpinejs/alpine@v2.7.0/dist/alpine.min.js" defer></script>
    <script>
        var datatable;
        var datatableElement;
        var searchElement;
        var columnArray;

        const apiRoute = `<?php echo e(route('prescription-list.api', ['importId' => $import_id])); ?>`;

        var datatableElement = $('prescription_dt');

        columnArray = [
            {
                field: 'script_date',
                title: 'Script Date',
                width: 100,
                sortable: true,
                autoHide: false,
            },
            {
                field: 'first_name',
                title: 'First Name',
                width: 200,
                sortable: true,
                autoHide: false,
            },
            {
                field: 'last_name',
                title: 'Last Name',
                width: 200,
                sortable: true,
                autoHide: false,
            },
            {
                field: 'medication',
                title: 'Medication',
                width: 150,
                sortable: true,
                autoHide: false,
            },
            {
                field: 'status',
                title: 'Status',
                width: 100,
                sortable: true,
                autoHide: false,
                template: function(data) {
                    return data.status;
                }
            }
        ];

        datatable = datatableElement.KTDatatable({
            data: {
                type: 'remote',
                source: {
                    read: {
                        url: apiRoute,
                        headers: {
                            'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
                        },
                        params: function() {
                            const query = datatable.getDataSourceQuery();

                            return {
                                import_id: query.import_id || '',

                            };
                        },
                        map: function(raw) {
                            var dataSet = raw;
                            if (typeof raw.data !== 'undefined') {
                                dataSet = raw.data;
                            }
                            return dataSet;
                        },
                    },
                },
                pageSize: 10,
                serverPaging: true,
                serverFiltering: true,
                serverSorting: true,
            },
            pagination: true,
            sortable: true,
            columns: columnArray,
        });

        // datatable.setDataSourceQuery({
        //     query: {

        //     }
        // });




        // document.addEventListener('DOMContentLoaded', function() {
        //     var datatable;

        //     console.log('DataTable element found:', datatableElement);
        //     console.log('API URL:', `<?php echo e(route('prescription-list.api', ['importId' => $import_id])); ?>`);

        //     if (datatableElement) {
        //         // Define columns for the datatable
        //         var columnArray = [{
        //                 field: 'id',
        //                 title: '#',
        //                 width: 50,
        //                 sortable: true,
        //                 autoHide: false,
        //             },
        //             {
        //                 field: 'script_date',
        //                 title: 'Script Date',
        //                 width: 100,
        //                 sortable: true,
        //                 autoHide: false,
        //             },
        //             {
        //                 field: 'patient_name',
        //                 title: 'Patient Name',
        //                 width: 200,
        //                 sortable: true,
        //                 autoHide: false,
        //                 template: function(data) {
        //                     return data.first_name + ' ' + data.last_name;
        //                 }
        //             },
        //             {
        //                 field: 'medication',
        //                 title: 'Medication',
        //                 width: 150,
        //                 sortable: true,
        //                 autoHide: false,
        //             },
        //             {
        //                 field: 'Actions',
        //                 title: 'Actions',
        //                 sortable: false,
        //                 width: 100,
        //                 overflow: 'visible',
        //                 autoHide: false,
        //                 template: function(data) {
        //                     const viewRoute = `<?php echo e(route('scripts.show-pdf', ['importFile' => '::ID'])); ?>`;
        //                     const viewUrl = viewRoute.replace('::ID', data.id);

        //                     return `
    //                         <a href="${viewUrl}" class="btn btn-sm btn-clean btn-icon" data-toggle="tooltip" title="View Script">
    //                             <i class="fas fa-eye"></i>
    //                         </a>
    //                     `;
        //                 }
        //             }
        //         ];

        //         // Initialize datatable
        //         datatable = datatableElement.KTDatatable({
        //             data: {
        //                 type: 'remote',
        //                 source: {
        //                     read: {
        //                         url: `<?php echo e(route('prescription-list.api', ['importId' => $import_id])); ?>`,
        //                         headers: {
        //                             'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
        //                         },
        //                         map: function(raw) {
        //                             var dataSet = raw;
        //                             if (typeof raw.data !== 'undefined') {
        //                                 dataSet = raw.data;
        //                             }
        //                             return dataSet;
        //                         },
        //                     },
        //                 },
        //                 pageSize: 10,
        //                 serverPaging: true,
        //                 serverFiltering: true,
        //                 serverSorting: true,
        //             },
        //             layout: {
        //                 scroll: false,
        //                 footer: false
        //             },
        //             sortable: true,
        //             pagination: true,
        //             columns: columnArray
        //         });
        //     }
        // });
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\KodeCreators\newlife-panel\resources\views/excel-import/staff-view.blade.php ENDPATH**/ ?>